<?php
// edit.php - Edit student form and logic

// Include database connection
include 'db.php';

// Initialize variables
$id = '';
$name = '';
$email = '';
$course = '';
$message = '';
$messageType = '';

// Get student ID from URL
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $id = (int)$_GET['id'];

    // Fetch student data
    $stmt = $conn->prepare("SELECT id, name, email, course FROM students WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $student = $result->fetch_assoc();
        $name = $student['name'];
        $email = $student['email'];
        $course = $student['course'];
    } else {
        // Student not found
        $stmt->close();
        closeConnection($conn);
        header("Location: index.php?message=Student not found&type=error");
        exit();
    }
    $stmt->close();
} else {
    // No ID provided
    closeConnection($conn);
    header("Location: index.php?message=Invalid student ID&type=error");
    exit();
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data and sanitize
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $course = sanitizeInput($_POST['course']);

    // Validation
    $errors = [];

    if (empty($name)) {
        $errors[] = "Name is required";
    } elseif (!isValidName($name)) {
        $errors[] = "Name can only contain letters, spaces, hyphens, and apostrophes";
    }

    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!isValidEmail($email)) {
        $errors[] = "Please enter a valid email address";
    }

    if (empty($course)) {
        $errors[] = "Course is required";
    } elseif (!isValidCourse($course)) {
        $errors[] = "Course name contains invalid characters";
    }

    // If no errors, update database
    if (empty($errors)) {
        $stmt = $conn->prepare("UPDATE students SET name = ?, email = ?, course = ? WHERE id = ?");
        $stmt->bind_param("sssi", $name, $email, $course, $id);

        if ($stmt->execute()) {
            // Success
            $stmt->close();
            closeConnection($conn);
            header("Location: index.php?message=Student updated successfully&type=success");
            exit();
        } else {
            // Check if it's a duplicate email error
            if ($conn->errno == 1062) {
                $message = "Error: Email already exists. Please use a different email.";
            } else {
                $message = "Error updating student: " . $conn->error;
            }
            $messageType = 'error';
        }
        $stmt->close();
    } else {
        $message = implode("<br>", $errors);
        $messageType = 'error';
    }
}

closeConnection($conn);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Student - Student Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Student Management System</h1>
        
        <!-- Navigation -->
        <nav class="nav-menu">
            <a href="index.php" class="nav-link">View Students</a>
            <a href="add.php" class="nav-link">Add Student</a>
        </nav>

        <!-- Content -->
        <div class="content">
            <h2>Edit Student</h2>

            <!-- Display messages -->
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- Edit student form -->
            <form method="POST" action="edit.php?id=<?php echo $id; ?>" class="student-form">
                <div class="form-group">
                    <label for="name">Full Name:</label>
                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>"
                           placeholder="Enter student's full name" required>
                </div>

                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>"
                           placeholder="Enter student's email" required>
                </div>

                <div class="form-group">
                    <label for="course">Course/Program:</label>
                    <input type="text" id="course" name="course" value="<?php echo htmlspecialchars($course); ?>"
                           placeholder="Enter course or program name" required>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Update Student</button>
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
