/* Simple CSS for Student CRUD */

body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 5px;
}

h1 {
    text-align: center;
    color: #333;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

h2 {
    color: #333;
    margin-bottom: 15px;
}

/* Simple Navigation */
.nav-menu {
    text-align: center;
    margin-bottom: 20px;
}

.nav-link {
    display: inline-block;
    padding: 10px 20px;
    margin: 0 5px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 3px;
}

.nav-link:hover,
.nav-link.active {
    background-color: #0056b3;
}

/* Messages */
.message {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 3px;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Simple Table */
.students-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.students-table th,
.students-table td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
}

.students-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Simple Buttons */
.btn {
    display: inline-block;
    padding: 8px 15px;
    margin: 2px;
    text-decoration: none;
    border-radius: 3px;
    color: white;
    font-size: 14px;
}

.btn-primary {
    background-color: #007bff;
}

.btn-edit {
    background-color: #28a745;
}

.btn-delete {
    background-color: #dc3545;
}

.btn:hover {
    opacity: 0.8;
}

/* Simple Form */
.student-form {
    max-width: 500px;
    margin: 20px auto;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.form-actions {
    text-align: center;
    margin-top: 20px;
}

.btn-secondary {
    background-color: #6c757d;
}

/* No data message */
.no-data {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Delete confirmation */
.delete-confirmation {
    max-width: 500px;
    margin: 20px auto;
    text-align: center;
}

.student-details {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 3px;
    margin: 15px 0;
    text-align: left;
}

.warning-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 3px;
    margin: 15px 0;
}
