<?php
// delete.php - Delete student logic

// Include database connection
include 'db.php';

// Initialize variables
$id = '';
$student = null;
$message = '';
$messageType = '';

// Get student ID from URL
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $id = (int)$_GET['id'];

    // Fetch student data to show confirmation
    $stmt = $conn->prepare("SELECT id, name, email, course FROM students WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $student = $result->fetch_assoc();
    } else {
        // Student not found
        $stmt->close();
        closeConnection($conn);
        header("Location: index.php?message=Student not found&type=error");
        exit();
    }
    $stmt->close();
} else {
    // No ID provided
    closeConnection($conn);
    header("Location: index.php?message=Invalid student ID&type=error");
    exit();
}

// Check if delete is confirmed
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    // Delete the student
    $stmt = $conn->prepare("DELETE FROM students WHERE id = ?");
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        // Success
        $stmt->close();
        closeConnection($conn);
        header("Location: index.php?message=Student deleted successfully&type=success");
        exit();
    } else {
        $message = "Error deleting student: " . $conn->error;
        $messageType = 'error';
    }
    $stmt->close();
}

closeConnection($conn);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Student - Student Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Student Management System</h1>
        
        <!-- Navigation -->
        <nav class="nav-menu">
            <a href="index.php" class="nav-link">View Students</a>
            <a href="add.php" class="nav-link">Add Student</a>
        </nav>

        <!-- Content -->
        <div class="content">
            <h2>Delete Student</h2>

            <!-- Display messages -->
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($student): ?>
                <!-- Delete confirmation -->
                <div class="delete-confirmation">
                    <h3>Are you sure you want to delete this student?</h3>

                    <div class="student-details">
                        <p><strong>Name:</strong> <?php echo htmlspecialchars($student['name']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($student['email']); ?></p>
                        <p><strong>Course:</strong> <?php echo htmlspecialchars($student['course']); ?></p>
                    </div>

                    <div class="warning-message">
                        <p><strong>Warning:</strong> This action cannot be undone!</p>
                    </div>

                    <form method="POST" action="delete.php?id=<?php echo $id; ?>" class="delete-form">
                        <div class="form-actions">
                            <button type="submit" name="confirm_delete" class="btn btn-delete"
                                    onclick="return confirm('Are you absolutely sure?')">
                                Yes, Delete Student
                            </button>
                            <a href="index.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
