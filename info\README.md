# Simple PHP CRUD Application

A beginner-friendly CRUD (Create, Read, Update, Delete) web application built with pure PHP and MySQL.

## Project Structure

```
student-crud/
├── index.php          # Main page - displays all students
├── add.php            # Add new student form and logic
├── edit.php           # Edit student form and logic
├── delete.php         # Delete student logic
├── db.php             # Database connection file
├── style.css          # Basic CSS styling
├── database.sql       # SQL script to create database and table
└── README.md          # This file
```

## Features

- **Create**: Add new students with name, email, and course
- **Read**: View all students in a table format
- **Update**: Edit existing student information
- **Delete**: Remove students from the database

## Requirements

- PHP 7.0 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx) or XAMPP/WAMP for local development

## Setup Instructions

1. **Database Setup**:
   - Create a MySQL database named `student_db`
   - Run the SQL script in `database.sql` to create the students table

2. **Configuration**:
   - Update database credentials in `db.php` if needed
   - Default settings: host=localhost, username=root, password=empty

3. **Run the Application**:
   - Place files in your web server directory (htdocs for XAMPP)
   - Access via browser: `http://localhost/student-crud/`

## Security Features

- Prepared statements to prevent SQL injection
- Input validation and sanitization
- Error handling for database operations

## Learning Objectives

This project demonstrates:
- Basic PHP syntax and file structure
- MySQL database operations
- HTML form handling
- Simple web application architecture
- Security best practices for beginners
