<?php
// test_connection.php - Simple test to verify database connection
include 'db.php';

echo "<h2>Database Connection Test</h2>";

// Test connection
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Connection failed: " . $conn->connect_error . "</p>";
} else {
    echo "<p style='color: green;'>✅ Connected successfully to database: " . $database . "</p>";
    
    // Test if students table exists
    $result = $conn->query("SHOW TABLES LIKE 'students'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Students table exists</p>";
        
        // Count students
        $count_result = $conn->query("SELECT COUNT(*) as count FROM students");
        $count = $count_result->fetch_assoc()['count'];
        echo "<p style='color: blue;'>📊 Number of students in database: " . $count . "</p>";
        
        if ($count > 0) {
            echo "<p style='color: green;'>✅ Sample data is present</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No students found - you may need to add some data</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Students table does not exist - please import database.sql</p>";
    }
}

echo "<hr>";
echo "<p><a href='index.php'>Go to Main Application</a></p>";
echo "<p><a href='http://localhost/phpmyadmin' target='_blank'>Open phpMyAdmin</a></p>";
?>
