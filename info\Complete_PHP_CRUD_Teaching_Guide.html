<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete PHP CRUD Tutorial - From Zero to Working Application</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            max-width: 1200px;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007acc;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .header p {
            font-size: 18px;
            color: #666;
        }
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #007acc;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .section h3 {
            color: #0056b3;
            margin-top: 25px;
            font-size: 1.4em;
        }
        .section h4 {
            color: #333;
            margin-top: 20px;
            font-size: 1.2em;
        }
        .step-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .code-section {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-box {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 15px 0;
        }
        .important-note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .checklist {
            list-style-type: none;
            padding-left: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        .checklist li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .file-structure {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
        }
        .url-highlight {
            background-color: #e7f3ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .page-break {
            page-break-before: always;
        }
        ol, ul {
            padding-left: 25px;
        }
        .filename {
            background-color: #e9ecef;
            padding: 3px 8px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        .explanation {
            background-color: #e8f4fd;
            border-left: 4px solid #007acc;
            padding: 15px;
            margin: 15px 0;
            font-style: italic;
        }
        .teacher-note {
            background-color: #fff8e1;
            border: 2px dashed #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Complete PHP CRUD Tutorial</h1>
        <p><strong>From Zero to Working Application</strong></p>
        <p>A Step-by-Step Guide for Teachers and Students</p>
        <p><em>Build a Student Management System from Scratch</em></p>
    </div>

    <div class="section">
        <h2>📋 Table of Contents</h2>
        <ol>
            <li><strong>Introduction & Setup</strong>
                <ul>
                    <li>What We'll Build</li>
                    <li>Installing XAMPP</li>
                    <li>Project Structure</li>
                </ul>
            </li>
            <li><strong>Database Creation</strong>
                <ul>
                    <li>Understanding Databases</li>
                    <li>Creating the Database</li>
                    <li>Creating Tables</li>
                </ul>
            </li>
            <li><strong>Coding the Application</strong>
                <ul>
                    <li>Database Connection (db.php)</li>
                    <li>Main Page - READ (index.php)</li>
                    <li>Add Student - CREATE (add.php)</li>
                    <li>Edit Student - UPDATE (edit.php)</li>
                    <li>Delete Student - DELETE (delete.php)</li>
                    <li>Styling (style.css)</li>
                </ul>
            </li>
            <li><strong>Testing & Troubleshooting</strong></li>
            <li><strong>Extensions & Next Steps</strong></li>
        </ol>
    </div>

    <div class="section page-break">
        <h2>🎯 Part 1: Introduction & Setup</h2>
        
        <h3>What We'll Build</h3>
        <div class="step-box">
            <p><strong>Student Management System</strong> - A web application that can:</p>
            <ul>
                <li><strong>CREATE:</strong> Add new students to the database</li>
                <li><strong>READ:</strong> Display all students in a table</li>
                <li><strong>UPDATE:</strong> Edit existing student information</li>
                <li><strong>DELETE:</strong> Remove students from the database</li>
            </ul>
        </div>

        <div class="teacher-note">
            <strong>🍎 Teacher Note:</strong> This tutorial assumes students have basic computer skills but no programming experience. Adjust pace accordingly and encourage questions!
        </div>

        <h3>Learning Objectives</h3>
        <div class="explanation">
            By the end of this tutorial, students will understand:
            <ul>
                <li>How web applications work (client-server model)</li>
                <li>Basic database concepts (tables, rows, columns)</li>
                <li>PHP syntax and file structure</li>
                <li>HTML forms and user interaction</li>
                <li>How to connect a website to a database</li>
            </ul>
        </div>

        <h3>Time Required</h3>
        <ul>
            <li><strong>Setup:</strong> 20 minutes</li>
            <li><strong>Database:</strong> 15 minutes</li>
            <li><strong>Coding:</strong> 90 minutes</li>
            <li><strong>Testing:</strong> 15 minutes</li>
            <li><strong>Total:</strong> 2.5 hours (can be split across multiple sessions)</li>
        </ul>
    </div>

    <div class="section">
        <h2>🔧 Installing XAMPP</h2>
        
        <div class="step-box">
            <h3>What is XAMPP?</h3>
            <p>XAMPP is a free software package that includes everything needed for web development:</p>
            <ul>
                <li><strong>X:</strong> Cross-platform (works on Windows, Mac, Linux)</li>
                <li><strong>A:</strong> Apache (web server - serves web pages)</li>
                <li><strong>M:</strong> MySQL (database server - stores data)</li>
                <li><strong>P:</strong> PHP (programming language - processes code)</li>
                <li><strong>P:</strong> Perl (another programming language)</li>
            </ul>
        </div>

        <h3>Download and Install</h3>
        <ol>
            <li>Go to: <span class="url-highlight">https://www.apachefriends.org/</span></li>
            <li>Click "Download" → "XAMPP for Windows"</li>
            <li>Download the latest version (~150MB)</li>
            <li>Run installer as administrator</li>
            <li>Follow installation wizard (keep all defaults)</li>
            <li>Install to: <code>C:\xampp</code></li>
        </ol>

        <div class="warning-box">
            <strong>⚠️ Common Issues:</strong>
            <ul>
                <li>Windows Firewall: Click "Allow access" when prompted</li>
                <li>Antivirus: May need to add XAMPP to exceptions</li>
                <li>Skype: Close Skype if it's running (conflicts with port 80)</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>📁 Project Structure</h2>
        
        <div class="step-box">
            <h3>Create Project Folder</h3>
            <ol>
                <li>Navigate to: <code>C:\xampp\htdocs\</code></li>
                <li>Create new folder: <code>student-crud</code></li>
                <li>This will be our project directory</li>
            </ol>
        </div>

        <h3>Files We'll Create</h3>
        <div class="file-structure">
student-crud/
├── database.sql       # Database setup script
├── db.php            # Database connection
├── index.php         # Main page (READ)
├── add.php           # Add student (CREATE)
├── edit.php          # Edit student (UPDATE)
├── delete.php        # Delete student (DELETE)
└── style.css         # Styling
        </div>

        <div class="explanation">
            <strong>Why this structure?</strong>
            <ul>
                <li>Each file has a specific purpose (separation of concerns)</li>
                <li>Easy to maintain and understand</li>
                <li>Follows common PHP conventions</li>
                <li>Scalable for future enhancements</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>🗄️ Part 2: Database Creation</h2>

        <h3>Understanding Databases</h3>
        <div class="explanation">
            <p><strong>Think of a database like a digital filing cabinet:</strong></p>
            <ul>
                <li><strong>Database:</strong> The entire filing cabinet (student_db)</li>
                <li><strong>Table:</strong> A drawer in the cabinet (students)</li>
                <li><strong>Columns:</strong> Categories of information (name, email, course)</li>
                <li><strong>Rows:</strong> Individual records (each student)</li>
            </ul>
        </div>

        <h3>Step 1: Start XAMPP Services</h3>
        <div class="step-box">
            <ol>
                <li>Open XAMPP Control Panel</li>
                <li>Start <strong>Apache</strong> (click Start button)</li>
                <li>Start <strong>MySQL</strong> (click Start button)</li>
                <li>Both should show green "Running" status</li>
            </ol>
        </div>

        <h3>Step 2: Create Database Script</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">database.sql</span></p>
            <div class="code-section">
-- database.sql - SQL script to create our database and table

-- Create database
CREATE DATABASE IF NOT EXISTS student_db;

-- Use the database
USE student_db;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    course VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample data for testing
INSERT INTO students (name, email, course) VALUES
('John Doe', '<EMAIL>', 'Computer Science'),
('Jane Smith', '<EMAIL>', 'Information Technology'),
('Mike Johnson', '<EMAIL>', 'Software Engineering');
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>CREATE DATABASE:</strong> Creates a new database named 'student_db'</li>
                <li><strong>USE:</strong> Tells MySQL to use this database for following commands</li>
                <li><strong>CREATE TABLE:</strong> Creates a table with columns for student data</li>
                <li><strong>AUTO_INCREMENT:</strong> Automatically assigns unique ID numbers</li>
                <li><strong>PRIMARY KEY:</strong> Makes 'id' the unique identifier</li>
                <li><strong>NOT NULL:</strong> Field cannot be empty</li>
                <li><strong>UNIQUE:</strong> No duplicate emails allowed</li>
                <li><strong>TIMESTAMP:</strong> Automatically tracks when records are created/updated</li>
            </ul>
        </div>

        <h3>Step 3: Import Database</h3>
        <div class="step-box">
            <ol>
                <li>Open browser → <span class="url-highlight">http://localhost/phpmyadmin</span></li>
                <li>Click <strong>"Import"</strong> tab</li>
                <li>Click <strong>"Choose File"</strong></li>
                <li>Select your <code>database.sql</code> file</li>
                <li>Click <strong>"Go"</strong> button</li>
                <li>Wait for success message</li>
            </ol>
        </div>

        <div class="success-box">
            <strong>✅ Verify Success:</strong>
            <ul>
                <li>Left sidebar shows "student_db" database</li>
                <li>Click on "student_db" → see "students" table</li>
                <li>Click on "students" → see 3 sample records</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>💻 Part 3: Coding the Application</h2>

        <div class="teacher-note">
            <strong>🍎 Teaching Tip:</strong> Have students type the code rather than copy-paste. This helps them understand syntax and catch errors early.
        </div>

        <h3>File 1: Database Connection</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">db.php</span></p>
            <div class="code-section">
&lt;?php
// db.php - Database connection file

// Database configuration
$host = 'localhost';        // Database host
$username = 'root';         // Database username (XAMPP default)
$password = '';             // Database password (empty for XAMPP)
$database = 'student_db';   // Database name

// Create connection using mysqli
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to UTF-8
$conn->set_charset("utf8");

// Function to sanitize input data
function sanitizeInput($data) {
    $data = trim($data);                    // Remove whitespace
    $data = stripslashes($data);            // Remove backslashes
    $data = htmlspecialchars($data);        // Convert special characters
    return $data;
}

// Function to validate email format
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}
?&gt;
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>&lt;?php:</strong> Opens PHP code block</li>
                <li><strong>$variables:</strong> Store database connection details</li>
                <li><strong>new mysqli():</strong> Creates database connection object</li>
                <li><strong>connect_error:</strong> Checks if connection failed</li>
                <li><strong>die():</strong> Stops execution and shows error message</li>
                <li><strong>sanitizeInput():</strong> Cleans user input to prevent attacks</li>
                <li><strong>isValidEmail():</strong> Checks if email format is correct</li>
            </ul>
        </div>

        <h3>File 2: Main Page (READ Operation)</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">index.php</span></p>
            <div class="code-section">
&lt;?php
// index.php - Main page that displays all students
include 'db.php';

// Get all students from database
$sql = "SELECT * FROM students ORDER BY created_at DESC";
$result = $conn->query($sql);
?&gt;

&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Student Management System&lt;/title&gt;
    &lt;link rel="stylesheet" href="style.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Student Management System&lt;/h1&gt;

        &lt;div class="header-actions"&gt;
            &lt;a href="add.php" class="btn btn-primary"&gt;Add Student&lt;/a&gt;
        &lt;/div&gt;

        &lt;table class="student-table"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th&gt;ID&lt;/th&gt;
                    &lt;th&gt;Name&lt;/th&gt;
                    &lt;th&gt;Email&lt;/th&gt;
                    &lt;th&gt;Course&lt;/th&gt;
                    &lt;th&gt;Date Added&lt;/th&gt;
                    &lt;th&gt;Actions&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;?php if ($result->num_rows > 0): ?&gt;
                    &lt;?php while($row = $result->fetch_assoc()): ?&gt;
                        &lt;tr&gt;
                            &lt;td&gt;&lt;?php echo $row['id']; ?&gt;&lt;/td&gt;
                            &lt;td&gt;&lt;?php echo htmlspecialchars($row['name']); ?&gt;&lt;/td&gt;
                            &lt;td&gt;&lt;?php echo htmlspecialchars($row['email']); ?&gt;&lt;/td&gt;
                            &lt;td&gt;&lt;?php echo htmlspecialchars($row['course']); ?&gt;&lt;/td&gt;
                            &lt;td&gt;&lt;?php echo date('M j, Y', strtotime($row['created_at'])); ?&gt;&lt;/td&gt;
                            &lt;td&gt;
                                &lt;a href="edit.php?id=&lt;?php echo $row['id']; ?&gt;" class="btn btn-edit"&gt;Edit&lt;/a&gt;
                                &lt;a href="delete.php?id=&lt;?php echo $row['id']; ?&gt;" class="btn btn-delete"&gt;Delete&lt;/a&gt;
                            &lt;/td&gt;
                        &lt;/tr&gt;
                    &lt;?php endwhile; ?&gt;
                &lt;?php else: ?&gt;
                    &lt;tr&gt;
                        &lt;td colspan="6"&gt;No students found. &lt;a href="add.php"&gt;Add the first student&lt;/a&gt;&lt;/td&gt;
                    &lt;/tr&gt;
                &lt;?php endif; ?&gt;
            &lt;/tbody&gt;
        &lt;/table&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;

&lt;?php
// Close database connection
$conn->close();
?&gt;
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>include 'db.php':</strong> Loads database connection</li>
                <li><strong>SELECT * FROM students:</strong> Gets all student records</li>
                <li><strong>ORDER BY created_at DESC:</strong> Shows newest students first</li>
                <li><strong>$result->query():</strong> Executes the SQL query</li>
                <li><strong>while loop:</strong> Goes through each student record</li>
                <li><strong>fetch_assoc():</strong> Gets one row as an array</li>
                <li><strong>htmlspecialchars():</strong> Prevents XSS attacks</li>
                <li><strong>?id=:</strong> Passes student ID to edit/delete pages</li>
            </ul>
        </div>

        <h3>File 3: Add Student (CREATE Operation)</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">add.php</span></p>
            <div class="code-section">
&lt;?php
// add.php - Add new student to database
include 'db.php';

$message = '';
$error = '';

// Check if form was submitted
if ($_POST) {
    // Get form data
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $course = sanitizeInput($_POST['course']);

    // Validate input
    if (empty($name) || empty($email) || empty($course)) {
        $error = "All fields are required.";
    } elseif (!isValidEmail($email)) {
        $error = "Please enter a valid email address.";
    } else {
        // Check if email already exists
        $check_sql = "SELECT id FROM students WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Email already exists. Please use a different email.";
        } else {
            // Insert new student
            $sql = "INSERT INTO students (name, email, course) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sss", $name, $email, $course);

            if ($stmt->execute()) {
                $message = "Student added successfully!";
                // Redirect to main page after 2 seconds
                header("refresh:2;url=index.php");
            } else {
                $error = "Error adding student: " . $conn->error;
            }
        }
    }
}
?&gt;

&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Add Student&lt;/title&gt;
    &lt;link rel="stylesheet" href="style.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Add New Student&lt;/h1&gt;

        &lt;?php if ($message): ?&gt;
            &lt;div class="success"&gt;&lt;?php echo $message; ?&gt;&lt;/div&gt;
        &lt;?php endif; ?&gt;

        &lt;?php if ($error): ?&gt;
            &lt;div class="error"&gt;&lt;?php echo $error; ?&gt;&lt;/div&gt;
        &lt;?php endif; ?&gt;

        &lt;form method="POST" class="student-form"&gt;
            &lt;div class="form-group"&gt;
                &lt;label for="name"&gt;Student Name:&lt;/label&gt;
                &lt;input type="text" id="name" name="name" required
                       value="&lt;?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-group"&gt;
                &lt;label for="email"&gt;Email Address:&lt;/label&gt;
                &lt;input type="email" id="email" name="email" required
                       value="&lt;?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-group"&gt;
                &lt;label for="course"&gt;Course:&lt;/label&gt;
                &lt;input type="text" id="course" name="course" required
                       value="&lt;?php echo isset($_POST['course']) ? htmlspecialchars($_POST['course']) : ''; ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-actions"&gt;
                &lt;button type="submit" class="btn btn-primary"&gt;Add Student&lt;/button&gt;
                &lt;a href="index.php" class="btn btn-secondary"&gt;Cancel&lt;/a&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>$_POST:</strong> Contains form data when submitted</li>
                <li><strong>sanitizeInput():</strong> Cleans user input</li>
                <li><strong>empty():</strong> Checks if field is blank</li>
                <li><strong>prepare():</strong> Creates secure SQL statement</li>
                <li><strong>bind_param():</strong> Safely inserts values into SQL</li>
                <li><strong>"sss":</strong> Tells MySQL all 3 values are strings</li>
                <li><strong>execute():</strong> Runs the SQL statement</li>
                <li><strong>header("refresh"):</strong> Redirects after success</li>
            </ul>
        </div>

        <h3>File 4: Edit Student (UPDATE Operation)</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">edit.php</span></p>
            <div class="code-section">
&lt;?php
// edit.php - Edit existing student
include 'db.php';

$message = '';
$error = '';
$student = null;

// Get student ID from URL
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    header("Location: index.php");
    exit();
}

// Get student data
$sql = "SELECT * FROM students WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: index.php");
    exit();
}

$student = $result->fetch_assoc();

// Check if form was submitted
if ($_POST) {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $course = sanitizeInput($_POST['course']);

    // Validate input
    if (empty($name) || empty($email) || empty($course)) {
        $error = "All fields are required.";
    } elseif (!isValidEmail($email)) {
        $error = "Please enter a valid email address.";
    } else {
        // Check if email exists for other students
        $check_sql = "SELECT id FROM students WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $email, $id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Email already exists. Please use a different email.";
        } else {
            // Update student
            $update_sql = "UPDATE students SET name = ?, email = ?, course = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("sssi", $name, $email, $course, $id);

            if ($update_stmt->execute()) {
                $message = "Student updated successfully!";
                // Update student array for display
                $student['name'] = $name;
                $student['email'] = $email;
                $student['course'] = $course;
                header("refresh:2;url=index.php");
            } else {
                $error = "Error updating student: " . $conn->error;
            }
        }
    }
}
?&gt;

&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Edit Student&lt;/title&gt;
    &lt;link rel="stylesheet" href="style.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Edit Student&lt;/h1&gt;

        &lt;?php if ($message): ?&gt;
            &lt;div class="success"&gt;&lt;?php echo $message; ?&gt;&lt;/div&gt;
        &lt;?php endif; ?&gt;

        &lt;?php if ($error): ?&gt;
            &lt;div class="error"&gt;&lt;?php echo $error; ?&gt;&lt;/div&gt;
        &lt;?php endif; ?&gt;

        &lt;form method="POST" class="student-form"&gt;
            &lt;div class="form-group"&gt;
                &lt;label for="name"&gt;Student Name:&lt;/label&gt;
                &lt;input type="text" id="name" name="name" required
                       value="&lt;?php echo htmlspecialchars($student['name']); ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-group"&gt;
                &lt;label for="email"&gt;Email Address:&lt;/label&gt;
                &lt;input type="email" id="email" name="email" required
                       value="&lt;?php echo htmlspecialchars($student['email']); ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-group"&gt;
                &lt;label for="course"&gt;Course:&lt;/label&gt;
                &lt;input type="text" id="course" name="course" required
                       value="&lt;?php echo htmlspecialchars($student['course']); ?&gt;"&gt;
            &lt;/div&gt;

            &lt;div class="form-actions"&gt;
                &lt;button type="submit" class="btn btn-primary"&gt;Update Student&lt;/button&gt;
                &lt;a href="index.php" class="btn btn-secondary"&gt;Cancel&lt;/a&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>$_GET['id']:</strong> Gets student ID from URL</li>
                <li><strong>(int):</strong> Converts to integer for security</li>
                <li><strong>header("Location"):</strong> Redirects if invalid ID</li>
                <li><strong>exit():</strong> Stops script execution</li>
                <li><strong>UPDATE SET:</strong> SQL command to modify existing record</li>
                <li><strong>WHERE id = ?:</strong> Only update the specific student</li>
                <li><strong>bind_param("sssi"):</strong> 3 strings, 1 integer</li>
            </ul>
        </div>

        <h3>File 5: Delete Student (DELETE Operation)</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">delete.php</span></p>
            <div class="code-section">
&lt;?php
// delete.php - Delete student from database
include 'db.php';

$message = '';
$error = '';
$student = null;

// Get student ID from URL
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id <= 0) {
    header("Location: index.php");
    exit();
}

// Get student data to show confirmation
$sql = "SELECT * FROM students WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: index.php");
    exit();
}

$student = $result->fetch_assoc();

// Check if delete was confirmed
if (isset($_POST['confirm_delete'])) {
    $delete_sql = "DELETE FROM students WHERE id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("i", $id);

    if ($delete_stmt->execute()) {
        $message = "Student deleted successfully!";
        header("refresh:2;url=index.php");
    } else {
        $error = "Error deleting student: " . $conn->error;
    }
}
?&gt;

&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Delete Student&lt;/title&gt;
    &lt;link rel="stylesheet" href="style.css"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Delete Student&lt;/h1&gt;

        &lt;?php if ($message): ?&gt;
            &lt;div class="success"&gt;&lt;?php echo $message; ?&gt;&lt;/div&gt;
        &lt;?php else: ?&gt;
            &lt;div class="warning"&gt;
                &lt;h3&gt;Are you sure you want to delete this student?&lt;/h3&gt;
                &lt;p&gt;This action cannot be undone.&lt;/p&gt;
            &lt;/div&gt;

            &lt;div class="student-details"&gt;
                &lt;p&gt;&lt;strong&gt;Name:&lt;/strong&gt; &lt;?php echo htmlspecialchars($student['name']); ?&gt;&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Email:&lt;/strong&gt; &lt;?php echo htmlspecialchars($student['email']); ?&gt;&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Course:&lt;/strong&gt; &lt;?php echo htmlspecialchars($student['course']); ?&gt;&lt;/p&gt;
                &lt;p&gt;&lt;strong&gt;Date Added:&lt;/strong&gt; &lt;?php echo date('M j, Y', strtotime($student['created_at'])); ?&gt;&lt;/p&gt;
            &lt;/div&gt;

            &lt;form method="POST" class="delete-form"&gt;
                &lt;div class="form-actions"&gt;
                    &lt;button type="submit" name="confirm_delete" class="btn btn-danger"&gt;Yes, Delete Student&lt;/button&gt;
                    &lt;a href="index.php" class="btn btn-secondary"&gt;Cancel&lt;/a&gt;
                &lt;/div&gt;
            &lt;/form&gt;
        &lt;?php endif; ?&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
            </div>
        </div>

        <div class="explanation">
            <strong>Code Explanation:</strong>
            <ul>
                <li><strong>confirm_delete:</strong> Checks if user clicked delete button</li>
                <li><strong>DELETE FROM:</strong> SQL command to remove record</li>
                <li><strong>Two-step process:</strong> Show confirmation, then delete</li>
                <li><strong>Student details:</strong> Shows what will be deleted</li>
                <li><strong>Warning message:</strong> Alerts user action is permanent</li>
            </ul>
        </div>

        <h3>File 6: Styling</h3>
        <div class="step-box">
            <p>Create file: <span class="filename">style.css</span></p>
            <div class="code-section">
/* style.css - Basic styling for our application */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

/* Button styles */
.btn {
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: inline-block;
    margin: 5px;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-edit {
    background-color: #28a745;
    color: white;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

/* Table styles */
.student-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.student-table th,
.student-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.student-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Form styles */
.student-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

/* Message styles */
.success {
    background-color: #d4edda;
    color: #155724;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.warning {
    background-color: #fff3cd;
    color: #856404;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}
            </div>
        </div>

        <div class="explanation">
            <strong>CSS Explanation:</strong>
            <ul>
                <li><strong>* selector:</strong> Applies to all elements (reset default styles)</li>
                <li><strong>.container:</strong> Centers content and adds white background</li>
                <li><strong>.btn classes:</strong> Different button styles for different actions</li>
                <li><strong>.student-table:</strong> Makes data table look professional</li>
                <li><strong>.form-group:</strong> Organizes form elements</li>
                <li><strong>Message classes:</strong> Color-coded feedback for users</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>🧪 Part 4: Testing & Troubleshooting</h2>

        <h3>Step 1: Start Your Application</h3>
        <div class="step-box">
            <ol>
                <li>Make sure XAMPP Apache and MySQL are running</li>
                <li>Open browser</li>
                <li>Go to: <span class="url-highlight">http://localhost/student-crud/</span></li>
                <li>You should see the Student Management System</li>
            </ol>
        </div>

        <h3>Step 2: Test Each Operation</h3>
        <div class="step-box">
            <h4>✅ Test READ (View Students)</h4>
            <ul>
                <li>Should see 3 sample students in table</li>
                <li>Table should have proper columns and formatting</li>
            </ul>

            <h4>✅ Test CREATE (Add Student)</h4>
            <ol>
                <li>Click "Add Student" button</li>
                <li>Fill out form with your information</li>
                <li>Click "Add Student"</li>
                <li>Should redirect to main page with success message</li>
                <li>Your student should appear in the list</li>
            </ol>

            <h4>✅ Test UPDATE (Edit Student)</h4>
            <ol>
                <li>Click "Edit" for any student</li>
                <li>Change some information</li>
                <li>Click "Update Student"</li>
                <li>Should redirect with success message</li>
                <li>Changes should be visible in main list</li>
            </ol>

            <h4>✅ Test DELETE (Remove Student)</h4>
            <ol>
                <li>Click "Delete" for any student</li>
                <li>Review confirmation page</li>
                <li>Click "Yes, Delete Student"</li>
                <li>Should redirect with success message</li>
                <li>Student should be removed from list</li>
            </ol>
        </div>

        <h3>Common Issues & Solutions</h3>
        <div class="error-box">
            <h4>Issue: "This site can't be reached"</h4>
            <ul>
                <li>Check: Apache running in XAMPP?</li>
                <li>Check: Correct URL? (localhost/student-crud/)</li>
                <li>Try: Restart Apache</li>
            </ul>
        </div>

        <div class="error-box">
            <h4>Issue: "Connection failed"</h4>
            <ul>
                <li>Check: MySQL running in XAMPP?</li>
                <li>Check: Database imported correctly?</li>
                <li>Try: Restart MySQL</li>
            </ul>
        </div>

        <div class="error-box">
            <h4>Issue: "Parse error" or "Syntax error"</h4>
            <ul>
                <li>Check: Missing semicolons (;)</li>
                <li>Check: Unmatched quotes or brackets</li>
                <li>Check: PHP tags (&lt;?php and ?&gt;)</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🚀 Part 5: Extensions & Next Steps</h2>

        <h3>For Advanced Students</h3>
        <div class="step-box">
            <h4>Easy Extensions:</h4>
            <ul>
                <li>Add more fields (phone number, address, age)</li>
                <li>Add search functionality</li>
                <li>Add sorting by different columns</li>
                <li>Improve CSS styling with Bootstrap</li>
            </ul>

            <h4>Intermediate Extensions:</h4>
            <ul>
                <li>Add pagination for large datasets</li>
                <li>Add image upload for student photos</li>
                <li>Add data export (CSV, PDF)</li>
                <li>Add user authentication (login system)</li>
            </ul>

            <h4>Advanced Extensions:</h4>
            <ul>
                <li>Convert to use a PHP framework (Laravel)</li>
                <li>Add API endpoints for mobile apps</li>
                <li>Add real-time features with JavaScript</li>
                <li>Deploy to a web server</li>
            </ul>
        </div>

        <h3>Learning Path Forward</h3>
        <ol>
            <li><strong>Master the Basics:</strong> HTML, CSS, JavaScript</li>
            <li><strong>Advanced PHP:</strong> Object-oriented programming, frameworks</li>
            <li><strong>Database Design:</strong> Relationships, normalization, optimization</li>
            <li><strong>Security:</strong> Authentication, authorization, data protection</li>
            <li><strong>Modern Tools:</strong> Git, Composer, testing frameworks</li>
        </ol>
    </div>

    <div class="section">
        <h2>🎉 Conclusion</h2>

        <div class="success-box">
            <h3>Congratulations! You've successfully built a complete web application!</h3>
            <p>Your students now understand:</p>
            <ul class="checklist">
                <li>How web applications work</li>
                <li>Database design and operations</li>
                <li>PHP programming fundamentals</li>
                <li>HTML forms and user interaction</li>
                <li>Basic security practices</li>
                <li>Debugging and troubleshooting</li>
            </ul>
        </div>

        <div class="teacher-note">
            <strong>🍎 Teaching Tips for Success:</strong>
            <ul>
                <li>Encourage students to experiment and break things</li>
                <li>Have them explain the code back to you</li>
                <li>Assign small modifications as homework</li>
                <li>Create a class showcase of their applications</li>
                <li>Connect this project to real-world applications</li>
            </ul>
        </div>

        <div class="important-note">
            <strong>🚀 This foundation will serve them well in their programming journey!</strong>
            <br><br>
            Remember: The best way to learn programming is by doing. Encourage your students to:
            <ul>
                <li>Practice regularly</li>
                <li>Build their own projects</li>
                <li>Join programming communities</li>
                <li>Never stop learning!</li>
            </ul>
        </div>
    </div>

    <div style="text-align: center; margin-top: 40px; color: #666; font-style: italic; border-top: 1px solid #ddd; padding-top: 20px;">
        <p>This comprehensive teaching guide covers everything needed to build a PHP CRUD application from scratch.</p>
        <p>Perfect for computer science classes, coding bootcamps, or self-study.</p>
        <p><strong>Happy Teaching! 🎓</strong></p>
    </div>
</body>
</html>
