<?php
    // index.php - Main page to display all students

    // Include database connection
    include 'db.php';

    // Initialize variables
    $students = [];
    $message = '';
    $messageType = '';

    // Check if there's a message from other pages (add, edit, delete)


    // Fetch all students from database
    $sql = "SELECT id, name, email, course, created_at FROM students ORDER BY name ASC";
    $result = $conn->query($sql);

    if ($result) {
        if ($result->num_rows > 0) {
            // Store all students in an array
            while ($row = $result->fetch_assoc()) {
                $students[] = $row;
            }
        }
    } else {
        $message = "Error fetching students: " . $conn->error;
        $messageType = 'error';
    }

    // Close database connection
    closeConnection($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Student Management System</h1>
        
        <!-- Navigation -->
        <nav class="nav-menu">
            <a href="index.php" class="nav-link active">View Students</a>
            <a href="add.php" class="nav-link">Add Student</a>
        </nav>

        <!-- Content -->
        <div class="content">
            <h2>All Students</h2>

            <!-- Display messages -->
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Students table -->
            <?php if (!empty($students)): ?>
                <div class="table-container">
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Course</th>
                                <th>Date Added</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($student['id']); ?></td>
                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    <td><?php echo htmlspecialchars($student['course']); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($student['created_at'])); ?></td>
                                    <td class="actions">
                                        <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-edit">Edit</a>
                                        <a href="delete.php?id=<?php echo $student['id']; ?>" class="btn btn-delete"
                                           onclick="return confirm('Are you sure you want to delete this student?')">Delete</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="no-data">
                    <p>No students found. <a href="add.php">Add the first student</a></p>
                </div>
            <?php endif; ?>

            <!-- Add student button -->
            <div class="add-button-container">
                <a href="add.php" class="btn btn-primary">Add New Student</a>
            </div>
        </div>
    </div>
</body>
</html>
