# Simple PHP CRUD Application - Setup Guide

## Prerequisites

1. **XAMPP** (recommended) or any web server with PHP and MySQL
   - Download from: https://www.apachefriends.org/
   - Includes Apache, PHP, and MySQL

## Step-by-Step Setup

### 1. Install XAMPP
- Download and install XAMPP
- Start Apache and MySQL services from XAMPP Control Panel

### 2. Setup Project Files
- Copy all project files to: `C:\xampp\htdocs\student-crud\` (Windows)
- Or to: `/opt/lampp/htdocs/student-crud/` (Linux)

### 3. Create Database
**Option A: Using phpMyAdmin (Recommended for beginners)**
1. Open browser and go to: `http://localhost/phpmyadmin`
2. Click "Import" tab
3. Choose file: `database.sql`
4. Click "Go" button

**Option B: Using MySQL Command Line**
```bash
mysql -u root -p < database.sql
```

**Option C: Manual Setup**
1. Open phpMyAdmin
2. Create new database named: `student_db`
3. Copy and paste SQL from `database.sql` file
4. Execute the queries

### 4. Configure Database Connection (if needed)
- Open `db.php`
- Update these settings if your MySQL setup is different:
```php
$host = 'localhost';        // Usually localhost
$username = 'root';         // Default XAMPP username
$password = '';             // Default XAMPP password (empty)
$database = 'student_db';   // Database name
```

### 5. Test the Application
1. Open browser
2. Go to: `http://localhost/student-crud/`
3. You should see the Student Management System

## Testing Checklist

### ✅ Test READ Operation
- [ ] Visit `http://localhost/student-crud/`
- [ ] Should display list of 3 sample students
- [ ] Table should show: ID, Name, Email, Course, Date Added, Actions

### ✅ Test CREATE Operation
- [ ] Click "Add Student" or visit `http://localhost/student-crud/add.php`
- [ ] Fill out the form with valid data
- [ ] Submit form
- [ ] Should redirect to main page with success message
- [ ] New student should appear in the list

### ✅ Test UPDATE Operation
- [ ] Click "Edit" button for any student
- [ ] Modify the student information
- [ ] Submit form
- [ ] Should redirect to main page with success message
- [ ] Changes should be visible in the list

### ✅ Test DELETE Operation
- [ ] Click "Delete" button for any student
- [ ] Should show confirmation page with student details
- [ ] Click "Yes, Delete Student"
- [ ] Should redirect to main page with success message
- [ ] Student should be removed from the list

### ✅ Test Validation
- [ ] Try adding student with empty fields (should show errors)
- [ ] Try adding student with invalid email (should show error)
- [ ] Try adding student with duplicate email (should show error)

## Common Issues and Solutions

### Issue: "Connection failed"
**Solution:** 
- Make sure MySQL is running in XAMPP
- Check database credentials in `db.php`

### Issue: "Database not found"
**Solution:**
- Make sure you imported `database.sql`
- Check if database `student_db` exists in phpMyAdmin

### Issue: "Page not found"
**Solution:**
- Make sure files are in correct directory: `htdocs/student-crud/`
- Check if Apache is running in XAMPP

### Issue: "Permission denied"
**Solution:**
- Make sure XAMPP has proper permissions
- Try running XAMPP as administrator

## File Structure
```
student-crud/
├── index.php          # Main page (READ)
├── add.php            # Add student (CREATE)
├── edit.php           # Edit student (UPDATE)
├── delete.php         # Delete student (DELETE)
├── db.php             # Database connection
├── style.css          # Simple styling
├── database.sql       # Database setup script
├── README.md          # Project documentation
└── SETUP_GUIDE.md     # This setup guide
```

## Next Steps for Learning

1. **Add more features:**
   - Search functionality
   - Pagination for large datasets
   - Export to CSV/PDF

2. **Improve security:**
   - Add user authentication
   - Implement CSRF tokens
   - Add rate limiting

3. **Enhance UI:**
   - Add Bootstrap or other CSS framework
   - Make it responsive
   - Add JavaScript for better UX

4. **Database improvements:**
   - Add more tables (courses, departments)
   - Implement relationships
   - Add data backup features
