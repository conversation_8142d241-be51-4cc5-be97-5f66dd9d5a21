-- database.sql - SQL script to create database and table

-- Create database
CREATE DATABASE IF NOT EXISTS student_db;

-- Use the database
USE student_db;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    course VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert some sample data for testing
INSERT INTO students (name, email, course) VALUES
('<PERSON>', '<EMAIL>', 'Computer Science'),
('<PERSON>', '<EMAIL>', 'Information Technology'),
('<PERSON>', '<EMAIL>', 'Software Engineering');

-- Show the created table structure
DESCRIBE students;
