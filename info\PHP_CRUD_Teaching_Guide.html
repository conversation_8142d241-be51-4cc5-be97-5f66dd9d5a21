<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP CRUD Application - Complete Teaching Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007acc;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 18px;
            color: #666;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #007acc;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .section h3 {
            color: #0056b3;
            margin-top: 20px;
        }
        .step-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .error-box {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .checklist {
            list-style-type: none;
            padding-left: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        .checklist li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .file-structure {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
        }
        .url-highlight {
            background-color: #e7f3ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .page-break {
            page-break-before: always;
        }
        ol, ul {
            padding-left: 25px;
        }
        .important-note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PHP CRUD Application Setup Guide</h1>
        <p>A Complete Step-by-Step Tutorial for Students</p>
        <p><strong>Teacher's Edition</strong> - Ready to use in classroom</p>
    </div>

    <div class="section">
        <h2>📋 Table of Contents</h2>
        <ol>
            <li>Introduction & Learning Objectives</li>
            <li>Prerequisites & What Students Need</li>
            <li>Step 1: Download and Install XAMPP</li>
            <li>Step 2: Understanding Project Structure</li>
            <li>Step 3: Setting Up Project Files</li>
            <li>Step 4: Starting XAMPP Services</li>
            <li>Step 5: Creating the Database</li>
            <li>Step 6: Testing the Setup</li>
            <li>Step 7: Running the Application</li>
            <li>Step 8: Testing All CRUD Features</li>
            <li>Troubleshooting Guide</li>
            <li>Assessment & Next Steps</li>
        </ol>
    </div>

    <div class="section page-break">
        <h2>🎯 Introduction & Learning Objectives</h2>
        
        <div class="step-box">
            <h3>What is CRUD?</h3>
            <ul>
                <li><strong>C</strong>reate: Add new data (students)</li>
                <li><strong>R</strong>ead: View existing data</li>
                <li><strong>U</strong>pdate: Modify existing data</li>
                <li><strong>D</strong>elete: Remove data</li>
            </ul>
        </div>

        <h3>By the end of this lesson, students will:</h3>
        <ul class="checklist">
            <li>Install and configure XAMPP web development environment</li>
            <li>Set up a MySQL database with sample data</li>
            <li>Run a complete PHP web application</li>
            <li>Test all CRUD operations (Create, Read, Update, Delete)</li>
            <li>Understand basic web development concepts</li>
            <li>Troubleshoot common setup issues</li>
        </ul>

        <div class="important-note">
            <strong>⏱️ Time Required:</strong> 45-60 minutes<br>
            <strong>👥 Class Size:</strong> Works for any size class<br>
            <strong>💻 Equipment:</strong> One computer per student with internet access
        </div>
    </div>

    <div class="section">
        <h2>📋 Prerequisites & What Students Need</h2>
        
        <h3>Before Starting:</h3>
        <ul>
            <li>Windows computer with administrator access</li>
            <li>Stable internet connection for downloads</li>
            <li>Basic understanding of files and folders</li>
            <li>Web browser (Chrome, Firefox, or Edge)</li>
        </ul>

        <div class="success-box">
            <strong>✅ No Prior Programming Experience Required!</strong><br>
            This tutorial is designed for complete beginners.
        </div>
    </div>

    <div class="section page-break">
        <h2>🔧 Step 1: Download and Install XAMPP</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Consider downloading XAMPP beforehand and sharing via USB drives to save time and bandwidth.
        </div>

        <h3>What is XAMPP?</h3>
        <div class="step-box">
            <p>XAMPP is a free software package that includes everything needed for web development:</p>
            <ul>
                <li><strong>Apache:</strong> Web server (serves web pages)</li>
                <li><strong>MySQL:</strong> Database server (stores data)</li>
                <li><strong>PHP:</strong> Programming language (processes code)</li>
                <li><strong>phpMyAdmin:</strong> Database management tool</li>
            </ul>
        </div>

        <h3>Download Instructions:</h3>
        <ol>
            <li>Open web browser</li>
            <li>Go to: <span class="url-highlight">https://www.apachefriends.org/</span></li>
            <li>Click "Download" button</li>
            <li>Select "XAMPP for Windows"</li>
            <li>Choose latest version (first option)</li>
            <li>Wait for download (~150MB)</li>
        </ol>

        <h3>Installation Instructions:</h3>
        <ol>
            <li>Locate downloaded file (usually in Downloads folder)</li>
            <li>Right-click installer → "Run as administrator"</li>
            <li>Click "Yes" when Windows asks for permission</li>
            <li>Follow installation wizard:
                <ul>
                    <li>Click "Next" on welcome screen</li>
                    <li>Keep all components selected (default)</li>
                    <li>Installation folder: Keep default <code>C:\xampp</code></li>
                    <li>Click "Next" through remaining screens</li>
                    <li>Click "Finish" when complete</li>
                </ul>
            </li>
        </ol>

        <div class="warning-box">
            <strong>⚠️ Important Notes:</strong>
            <ul>
                <li>If Windows Firewall asks, click "Allow access"</li>
                <li>Antivirus may block installation - add exception if needed</li>
                <li>Installation takes 5-10 minutes</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>📁 Step 2: Understanding Project Structure</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Explain each file's purpose before students start copying files. This helps them understand the application structure.
        </div>

        <h3>Project Files Overview:</h3>
        <div class="file-structure">
kuya-charl/
├── index.php          # Main page - shows all students
├── add.php            # Form to add new students  
├── edit.php           # Form to edit existing students
├── delete.php         # Page to delete students
├── db.php             # Database connection settings
├── style.css          # Makes pages look nice
├── database.sql       # Instructions to create database
├── test_connection.php # Tests if everything works
└── README.md          # Project information
        </div>

        <h3>Key Files Explained:</h3>
        <div class="step-box">
            <ul>
                <li><strong>index.php:</strong> The home page students will see first</li>
                <li><strong>database.sql:</strong> Contains instructions to create the database</li>
                <li><strong>db.php:</strong> Connects the website to the database</li>
                <li><strong>test_connection.php:</strong> Helps verify everything is working</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>📂 Step 3: Setting Up Project Files</h2>
        
        <h3>Locate XAMPP Folder:</h3>
        <ol>
            <li>Open File Explorer</li>
            <li>Navigate to: <code>C:\xampp\htdocs\</code></li>
            <li>You should see a folder called "htdocs"</li>
        </ol>

        <h3>Copy Project Files:</h3>
        <ol>
            <li>Copy the entire project folder <code>kuya-charl</code></li>
            <li>Paste it into <code>C:\xampp\htdocs\</code></li>
            <li>Final path should be: <code>C:\xampp\htdocs\kuya-charl\</code></li>
        </ol>

        <div class="success-box">
            <strong>✅ Verify File Location:</strong><br>
            Your files should now be at:
            <ul>
                <li><code>C:\xampp\htdocs\kuya-charl\index.php</code></li>
                <li><code>C:\xampp\htdocs\kuya-charl\database.sql</code></li>
                <li><code>C:\xampp\htdocs\kuya-charl\db.php</code></li>
                <li>And all other files...</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>🚀 Step 4: Starting XAMPP Services</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Walk around the classroom during this step to help students who encounter port conflicts or permission issues.
        </div>

        <h3>Open XAMPP Control Panel:</h3>
        <ol>
            <li>Press Windows key</li>
            <li>Type: "XAMPP"</li>
            <li>Click: "XAMPP Control Panel"</li>
            <li>If prompted: Run as administrator</li>
        </ol>

        <h3>Start Required Services:</h3>
        
        <div class="step-box">
            <h4>Start Apache:</h4>
            <ol>
                <li>Find the "Apache" row</li>
                <li>Click the "Start" button next to Apache</li>
                <li>Wait for it to turn green and show "Running"</li>
            </ol>

            <h4>Start MySQL:</h4>
            <ol>
                <li>Find the "MySQL" row</li>
                <li>Click the "Start" button next to MySQL</li>
                <li>Wait for it to turn green and show "Running"</li>
            </ol>
        </div>

        <div class="success-box">
            <strong>✅ Success Indicators:</strong>
            <ul>
                <li>Both Apache and MySQL show green "Running" status</li>
                <li>Port numbers appear (usually 80 for Apache, 3306 for MySQL)</li>
                <li>No error messages in the log area</li>
            </ul>
        </div>

        <div class="error-box">
            <strong>⚠️ If Services Won't Start:</strong>
            <ul>
                <li><strong>Port conflict:</strong> Change ports in XAMPP config</li>
                <li><strong>Antivirus blocking:</strong> Add XAMPP to exceptions</li>
                <li><strong>Skype conflict:</strong> Close Skype (uses port 80)</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>🗄️ Step 5: Creating the Database</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Demonstrate this step on a projector first, then have students follow along. This is often where students get confused.
        </div>

        <h3>Method: Using phpMyAdmin (Recommended)</h3>
        
        <div class="step-box">
            <h4>Step 5.1: Open phpMyAdmin</h4>
            <ol>
                <li>Open web browser</li>
                <li>Type in address bar: <span class="url-highlight">http://localhost/phpmyadmin</span></li>
                <li>Press Enter</li>
                <li>You should see phpMyAdmin interface</li>
            </ol>
        </div>

        <div class="step-box">
            <h4>Step 5.2: Import Database</h4>
            <ol>
                <li>Click "Import" tab at the top</li>
                <li>Click "Choose File" button</li>
                <li>Navigate to: <code>C:\xampp\htdocs\kuya-charl\database.sql</code></li>
                <li>Select <code>database.sql</code> file</li>
                <li>Click "Open"</li>
                <li>Scroll down and click "Go" button</li>
                <li>Wait for success message</li>
            </ol>
        </div>

        <div class="step-box">
            <h4>Step 5.3: Verify Database Creation</h4>
            <ol>
                <li>Look at left sidebar in phpMyAdmin</li>
                <li>You should see "student_db" database</li>
                <li>Click on "student_db"</li>
                <li>You should see "students" table</li>
                <li>Click on "students" table</li>
                <li>You should see 3 sample students (John, Jane, Mike)</li>
            </ol>
        </div>

        <div class="success-box">
            <strong>✅ Success Indicators:</strong>
            <ul>
                <li>Green success message appears</li>
                <li>"student_db" appears in left sidebar</li>
                <li>"students" table contains 3 rows of data</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>🧪 Step 6: Testing Your Setup</h2>
        
        <h3>Test Database Connection:</h3>
        <ol>
            <li>Open web browser</li>
            <li>Go to: <span class="url-highlight">http://localhost/kuya-charl/test_connection.php</span></li>
            <li>You should see success messages</li>
        </ol>

        <div class="success-box">
            <strong>✅ Expected Results:</strong>
            <ul>
                <li>✅ Connected successfully to database: student_db</li>
                <li>✅ Students table exists</li>
                <li>📊 Number of students in database: 3</li>
                <li>✅ Sample data is present</li>
            </ul>
        </div>

        <div class="error-box">
            <strong>⚠️ If You See Errors:</strong>
            <ul>
                <li><strong>Connection failed:</strong> MySQL not running → Go back to Step 4</li>
                <li><strong>Database not found:</strong> Import failed → Go back to Step 5</li>
                <li><strong>Page not found:</strong> Apache not running or wrong URL</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🌐 Step 7: Running Your Application</h2>
        
        <h3>Launch the Main Application:</h3>
        <ol>
            <li>Open web browser</li>
            <li>Go to: <span class="url-highlight">http://localhost/kuya-charl/</span></li>
            <li>You should see the Student Management System page</li>
        </ol>

        <div class="success-box">
            <strong>✅ What You Should See:</strong>
            <ul>
                <li><strong>Title:</strong> "Student Management System"</li>
                <li><strong>Table</strong> with columns: ID, Name, Email, Course, Date Added, Actions</li>
                <li><strong>Three students</strong> listed:
                    <ul>
                        <li>John Doe (Computer Science)</li>
                        <li>Jane Smith (Information Technology)</li>
                        <li>Mike Johnson (Software Engineering)</li>
                    </ul>
                </li>
                <li><strong>"Add Student" button</strong> at the top</li>
                <li><strong>"Edit" and "Delete" buttons</strong> for each student</li>
            </ul>
        </div>
    </div>

    <div class="section page-break">
        <h2>✅ Step 8: Testing All CRUD Features</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Have students work through each test systematically. This is great for assessment and ensures everyone understands each operation.
        </div>

        <div class="step-box">
            <h3>Test 1: READ Operation (View Students)</h3>
            <p><strong>What to do:</strong></p>
            <ul>
                <li>Look at the main page</li>
                <li>Verify you can see all 3 students</li>
            </ul>
            <p><strong>Expected result:</strong> Table displays all student information clearly</p>
        </div>

        <div class="step-box">
            <h3>Test 2: CREATE Operation (Add Student)</h3>
            <p><strong>What to do:</strong></p>
            <ol>
                <li>Click "Add Student" button</li>
                <li>Fill out the form:
                    <ul>
                        <li>Name: "Your Name"</li>
                        <li>Email: "<EMAIL>"</li>
                        <li>Course: "Web Development"</li>
                    </ul>
                </li>
                <li>Click "Add Student" button</li>
                <li>Check: You're redirected to main page</li>
                <li>Verify: New student appears in the list</li>
            </ol>
            <p><strong>Expected result:</strong> New student is visible in the table with success message</p>
        </div>

        <div class="step-box">
            <h3>Test 3: UPDATE Operation (Edit Student)</h3>
            <p><strong>What to do:</strong></p>
            <ol>
                <li>Click "Edit" button for any student</li>
                <li>Modify some information (e.g., change course)</li>
                <li>Click "Update Student" button</li>
                <li>Check: You're redirected to main page</li>
                <li>Verify: Changes are visible</li>
            </ol>
            <p><strong>Expected result:</strong> Updated information appears in table</p>
        </div>

        <div class="step-box">
            <h3>Test 4: DELETE Operation (Remove Student)</h3>
            <p><strong>What to do:</strong></p>
            <ol>
                <li>Click "Delete" button for any student</li>
                <li>Review student information on confirmation page</li>
                <li>Click "Yes, Delete Student" button</li>
                <li>Check: You're redirected to main page</li>
                <li>Verify: Student is removed from list</li>
            </ol>
            <p><strong>Expected result:</strong> Student is completely removed, remaining students still display</p>
        </div>
    </div>

    <div class="section page-break">
        <h2>🔧 Troubleshooting Guide</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Keep this section handy during class. These are the most common issues students encounter.
        </div>

        <div class="error-box">
            <h3>Issue: "This site can't be reached"</h3>
            <p><strong>Possible causes:</strong> Apache not running, wrong URL</p>
            <p><strong>Solutions:</strong></p>
            <ol>
                <li>Check XAMPP Control Panel - Apache should be green/running</li>
                <li>Verify URL: <code>http://localhost/kuya-charl/</code></li>
                <li>Try restarting Apache in XAMPP</li>
            </ol>
        </div>

        <div class="error-box">
            <h3>Issue: "Connection failed" error</h3>
            <p><strong>Possible causes:</strong> MySQL not running, wrong credentials</p>
            <p><strong>Solutions:</strong></p>
            <ol>
                <li>Check XAMPP Control Panel - MySQL should be green/running</li>
                <li>Restart MySQL in XAMPP</li>
                <li>Check db.php file for correct settings</li>
            </ol>
        </div>

        <div class="error-box">
            <h3>Issue: "Database 'student_db' doesn't exist"</h3>
            <p><strong>Possible causes:</strong> Database not imported correctly</p>
            <p><strong>Solutions:</strong></p>
            <ol>
                <li>Go back to Step 5 and re-import database.sql</li>
                <li>Check phpMyAdmin to see if student_db exists</li>
                <li>Manually create database if needed</li>
            </ol>
        </div>
    </div>

    <div class="section page-break">
        <h2>📊 Assessment & Next Steps</h2>
        
        <div class="tip-box">
            <strong>💡 Teacher Tip:</strong> Use this checklist to assess student progress and identify who needs additional help.
        </div>

        <h3>Student Assessment Checklist:</h3>
        <ul class="checklist">
            <li>Successfully installed XAMPP</li>
            <li>Started Apache and MySQL services</li>
            <li>Imported database correctly</li>
            <li>Accessed main application page</li>
            <li>Added a new student (CREATE)</li>
            <li>Viewed student list (READ)</li>
            <li>Edited student information (UPDATE)</li>
            <li>Deleted a student (DELETE)</li>
            <li>Troubleshot at least one issue independently</li>
        </ul>

        <h3>Extension Activities:</h3>
        <div class="step-box">
            <h4>For Advanced Students:</h4>
            <ul>
                <li>Add more fields to the student form (phone, address)</li>
                <li>Implement search functionality</li>
                <li>Add data validation (prevent duplicate emails)</li>
                <li>Improve the CSS styling</li>
            </ul>
        </div>

        <h3>Learning Path Forward:</h3>
        <ol>
            <li><strong>Master HTML/CSS</strong> for better web design</li>
            <li><strong>Learn JavaScript</strong> for interactive features</li>
            <li><strong>Study PHP frameworks</strong> like Laravel</li>
            <li><strong>Explore databases</strong> in more depth</li>
            <li><strong>Learn version control</strong> with Git</li>
        </ol>
    </div>

    <div class="section">
        <h2>🎉 Conclusion</h2>
        
        <div class="success-box">
            <h3>Congratulations! Your students have successfully:</h3>
            <ul class="checklist">
                <li>Installed a complete web development environment</li>
                <li>Set up a MySQL database with sample data</li>
                <li>Deployed a working PHP application</li>
                <li>Tested all CRUD operations</li>
                <li>Learned fundamental web development concepts</li>
            </ul>
        </div>

        <div class="important-note">
            <strong>🚀 This foundation will serve them well as they continue learning web development!</strong>
            <br><br>
            Encourage students to:
            <ul>
                <li>Practice regularly to reinforce concepts</li>
                <li>Experiment with the code to see how changes affect the application</li>
                <li>Not be afraid to break things - that's how they learn!</li>
                <li>Ask questions when they get stuck</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <p style="text-align: center; margin-top: 40px; color: #666; font-style: italic;">
            This teaching guide was created to help educators introduce PHP and MySQL fundamentals through hands-on practice.
        </p>
    </div>
</body>
</html>
