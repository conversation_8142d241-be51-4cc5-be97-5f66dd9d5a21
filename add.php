<?php
// add.php - Add new student form and logic

// Include database connection
include 'db.php';

// Initialize variables
$name = '';
$email = '';
$course = '';
$message = '';
$messageType = '';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data and sanitize
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $course = sanitizeInput($_POST['course']);

    // Validation
    $errors = [];

    // Check if fields are empty and valid
    if (empty($name)) {
        $errors[] = "Name is required";
    } elseif (!isValidName($name)) {
        $errors[] = "Name can only contain letters, spaces, hyphens, and apostrophes";
    }

    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!isValidEmail($email)) {
        $errors[] = "Please enter a valid email address";
    }

    if (empty($course)) {
        $errors[] = "Course is required";
    } elseif (!isValidCourse($course)) {
        $errors[] = "Course name contains invalid characters";
    }

    // If no errors, insert into database
    if (empty($errors)) {
        // Use prepared statement to prevent SQL injection
        $stmt = $conn->prepare("INSERT INTO students (name, email, course) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $name, $email, $course);

        if ($stmt->execute()) {
            // Success - redirect to index page with success message
            $stmt->close();
            closeConnection($conn);
            header("Location: index.php?message=Student added successfully&type=success");
            exit();
        } else {
            // Check if it's a duplicate email error
            if ($conn->errno == 1062) {
                $message = "Error: Email already exists. Please use a different email.";
            } else {
                $message = "Error adding student: " . $conn->error;
            }
            $messageType = 'error';
        }
        $stmt->close();
    } else {
        // Display validation errors
        $message = implode("<br>", $errors);
        $messageType = 'error';
    }
}

// Close database connection
closeConnection($conn);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - Student Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Student Management System</h1>
        
        <!-- Navigation -->
        <nav class="nav-menu">
            <a href="index.php" class="nav-link">View Students</a>
            <a href="add.php" class="nav-link active">Add Student</a>
        </nav>

        <!-- Content -->
        <div class="content">
            <h2>Add New Student</h2>

            <!-- Display messages -->
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- Add student form -->
            <form method="POST" action="add.php" class="student-form">
                <div class="form-group">
                    <label for="name">Full Name:</label>
                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>"
                           placeholder="Enter student's full name" required>
                </div>

                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>"
                           placeholder="Enter student's email" required>
                </div>

                <div class="form-group">
                    <label for="course">Course/Program:</label>
                    <input type="text" id="course" name="course" value="<?php echo htmlspecialchars($course); ?>"
                           placeholder="Enter course or program name" required>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Add Student</button>
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
