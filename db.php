<?php
// db.php - Database connection file
// This file contains the database connection logic using mysqli

// Database configuration
$host = 'localhost';        // Database host (usually localhost)
$username = 'root';         // Database username (default for XAMPP)
$password = '';             // Database password (empty for XAMPP)
$database = 'student_db';   // Database name

// Create connection using mysqli
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    // If connection fails, stop execution and show error
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to UTF-8 for proper character encoding
$conn->set_charset("utf8");

// Optional: You can uncomment the line below for debugging
// echo "Connected successfully to database: " . $database;

/**
 * Function to close database connection
 * Call this function when you're done with database operations
 */
function closeConnection($connection) {
    if ($connection) {
        $connection->close();
    }
}

/**
 * Function to sanitize input data
 * This helps prevent basic XSS attacks
 */
function sanitizeInput($data) {
    $data = trim($data);                    // Remove whitespace
    $data = stripslashes($data);            // Remove backslashes
    $data = htmlspecialchars($data);        // Convert special characters
    return $data;
}

/**
 * Function to validate email format
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Function to validate student name (letters, spaces, and common punctuation only)
 */
function isValidName($name) {
    // Allow letters, spaces, hyphens, apostrophes, and dots
    return preg_match("/^[a-zA-Z\s\-'.]+$/", $name);
}

/**
 * Function to validate course name
 */
function isValidCourse($course) {
    // Allow letters, numbers, spaces, and common punctuation
    return preg_match("/^[a-zA-Z0-9\s\-'.&()]+$/", $course);
}

// Note: The $conn variable is now available in any file that includes this file
// Example usage in other files:
// include 'db.php';
// $result = $conn->query("SELECT * FROM students");
?>
